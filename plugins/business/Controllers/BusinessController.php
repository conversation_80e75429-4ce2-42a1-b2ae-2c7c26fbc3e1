<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Tag;
use Plugins\Products\Models\Product;
use Plugins\Business\Models\BusinessActivity;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class BusinessController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            // Check if user has any business-related permissions
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'show' => 'view_businesses',
            'create' => 'manage_businesses',
            'store' => 'manage_businesses',
            'edit' => 'manage_businesses',
            'update' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'users' => 'manage_business_users',
            'assignUser' => 'manage_business_users',
            'removeUser' => 'manage_business_users',
            'reports' => 'view_business_reports',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display a listing of businesses
     */
    public function index(Request $request): View
    {
        // Validate search parameters
        $request->validate([
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|array',
            'status.*' => 'in:lead,deal,customer,partner,churned,lost',
            'products' => 'nullable|array',
            'products.*' => 'integer|exists:products,id',
            'tags' => 'nullable|array',
            'tags.*' => 'integer|exists:tags,id',
        ]);

        $query = Business::with(['creator', 'users', 'tags', 'products']);

        // Unified search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('brand_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('taqnyat_id', 'like', "%{$search}%")
                  ->orWhere('taqnyat_username', 'like', "%{$search}%");
            });
        }

        // Filter by status (multiple selection)
        if ($request->filled('status')) {
            $statuses = $request->get('status');
            if (is_array($statuses) && !empty($statuses)) {
                $query->whereIn('status', $statuses);
            }
        }

        // Filter by products
        if ($request->filled('products')) {
            $productIds = $request->get('products');
            if (is_array($productIds) && !empty($productIds)) {
                $query->whereHas('products', function ($q) use ($productIds) {
                    $q->whereIn('products.id', $productIds);
                });
            }
        }

        // Filter by tags
        if ($request->filled('tags')) {
            $tagIds = $request->get('tags');
            if (is_array($tagIds) && !empty($tagIds)) {
                $query->whereHas('tags', function ($q) use ($tagIds) {
                    $q->whereIn('tags.id', $tagIds);
                });
            }
        }

        $businesses = $query->latest()->paginate(10);

        return view('plugins.business::index', compact('businesses'));
    }

    /**
     * Show the form for creating a new business
     */
    public function create(): View
    {
        $statuses = Business::getStatuses();
        $tags = Tag::active()->orderBy('name')->get();
        $products = Product::active()->orderBy('name')->get();

        return view('plugins.business::create', compact('statuses', 'tags', 'products'));
    }

    /**
     * Store a newly created business
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'arabic_name' => 'nullable|string|max:255',
            'brand_name' => 'nullable|string|max:255',
            'legal_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'primary_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'website_url' => 'nullable|url|max:255',
            'tax_id' => 'nullable|string|max:50',
            'status' => 'required|in:lead,deal,customer,partner,churned,lost',
            'churn_reason' => 'nullable|required_if:status,churned|string|max:1000',
            'churned_at' => 'nullable|required_if:status,churned|date',
            'lost_reason' => 'nullable|required_if:status,lost|string|max:1000',
            'lost_at' => 'nullable|required_if:status,lost|date',
            'tag_ids' => 'nullable|array',
            'tag_ids.*' => 'exists:tags,id',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'exists:products,id',
            // WhatsApp Business Integration
            'whatsapp_enabled' => 'boolean',
            'meta_business_id' => 'nullable|string|max:255',
            'whatsapp_id' => 'nullable|string|max:255',
            'whatsapp_provider' => 'nullable|in:taqnyat,360dialog',
            'message_quality' => 'nullable|string|max:50',
            'messaging_tier' => 'nullable|string|max:50',
            'meta_business_verified' => 'boolean',
            'whatsapp_business_verified' => 'boolean',
            // Taqnyat Integration
            // Taqnyat Integration
            'taqnyat_id' => 'nullable|string|max:255',
            'taqnyat_username' => 'nullable|string|max:255',
        ]);

        $business = Business::create([
            'name' => $request->name,
            'arabic_name' => $request->arabic_name,
            'brand_name' => $request->brand_name,
            'legal_name' => $request->legal_name,
            'description' => $request->description,
            'email' => $request->email,
            'phone' => $request->phone,
            'primary_phone' => $request->primary_phone,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'website' => $request->website,
            'website_url' => $request->website_url,
            'tax_id' => $request->tax_id,
            'status' => $request->status,
            'churn_reason' => $request->churn_reason,
            'churned_at' => $request->status === 'churned' ? $request->churned_at : null,
            'lost_reason' => $request->lost_reason,
            'lost_at' => $request->status === 'lost' ? $request->lost_at : null,
            'created_by' => Auth::id(),
            // WhatsApp Business Integration
            'whatsapp_enabled' => $request->boolean('whatsapp_enabled', false),
            'meta_business_id' => $request->meta_business_id,
            'whatsapp_id' => $request->whatsapp_id,
            'whatsapp_provider' => $request->whatsapp_provider,
            'message_quality' => $request->message_quality,
            'messaging_tier' => $request->messaging_tier,
            'meta_business_verified' => $request->boolean('meta_business_verified', false),
            'whatsapp_business_verified' => $request->boolean('whatsapp_business_verified', false),
            // Taqnyat Integration
            'taqnyat_id' => $request->taqnyat_id,
            'taqnyat_username' => $request->taqnyat_username,
        ]);

        // Assign tags if provided
        if ($request->filled('tag_ids')) {
            foreach ($request->tag_ids as $tagId) {
                $tag = Tag::find($tagId);
                if ($tag) {
                    $business->assignTag($tag, Auth::user());
                }
            }
        }

        // Assign products if provided
        if ($request->filled('product_ids')) {
            foreach ($request->product_ids as $productId) {
                $product = Product::find($productId);
                if ($product) {
                    $business->assignProduct($product, Auth::user());
                }
            }
        }

        // Log business creation activity
        BusinessActivity::logUserAction(
            $business,
            'business_update',
            'New business created',
            "Business '{$business->name}' has been created",
            [
                'initial_status' => $business->status,
                'tags_assigned' => count($request->tag_ids ?? []),
                'products_assigned' => count($request->product_ids ?? []),
            ]
        );

        return redirect()->route('business.index')
                        ->with('success', 'Business created successfully.');
    }

    /**
     * Display the specified business
     */
    public function show(Business $business): View
    {
        $business->load(['creator', 'users.role', 'contacts', 'documents', 'tags', 'products', 'activities']);
        return view('plugins.business::show', compact('business'));
    }

    /**
     * Show the form for editing the specified business
     */
    public function edit(Business $business): View
    {
        $statuses = Business::getStatuses();
        $tags = Tag::active()->orderBy('name')->get();
        $products = Product::active()->orderBy('name')->get();

        // Get currently assigned tags and products
        $assignedTagIds = $business->tags()->pluck('tags.id')->toArray();
        $assignedProductIds = $business->products()->pluck('products.id')->toArray();

        return view('plugins.business::edit', compact('business', 'statuses', 'tags', 'products', 'assignedTagIds', 'assignedProductIds'));
    }

    /**
     * Update the specified business
     */
    public function update(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'arabic_name' => 'nullable|string|max:255',
            'brand_name' => 'nullable|string|max:255',
            'legal_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'primary_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'website_url' => 'nullable|url|max:255',
            'tax_id' => 'nullable|string|max:50',
            'status' => 'required|in:lead,deal,customer,partner,churned,lost',
            'churn_reason' => 'nullable|required_if:status,churned|string|max:1000',
            'churned_at' => 'nullable|required_if:status,churned|date',
            'lost_reason' => 'nullable|required_if:status,lost|string|max:1000',
            'lost_at' => 'nullable|required_if:status,lost|date',
            'tag_ids' => 'nullable|array',
            'tag_ids.*' => 'exists:tags,id',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'exists:products,id',
            // WhatsApp Business Integration
            'whatsapp_enabled' => 'boolean',
            'meta_business_id' => 'nullable|string|max:255',
            'whatsapp_id' => 'nullable|string|max:255',
            'whatsapp_provider' => 'nullable|in:taqnyat,360dialog',
            'message_quality' => 'nullable|string|max:50',
            'messaging_tier' => 'nullable|string|max:50',
        ]);

        // Check for status change before updating
        $oldStatus = $business->status;
        $newStatus = $request->status;

        $business->update([
            'name' => $request->name,
            'arabic_name' => $request->arabic_name,
            'brand_name' => $request->brand_name,
            'legal_name' => $request->legal_name,
            'description' => $request->description,
            'email' => $request->email,
            'phone' => $request->phone,
            'primary_phone' => $request->primary_phone,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'website' => $request->website,
            'website_url' => $request->website_url,
            'tax_id' => $request->tax_id,
            'status' => $request->status,
            'churn_reason' => $request->churn_reason,
            'churned_at' => $request->status === 'churned' ? $request->churned_at : null,
            'lost_reason' => $request->lost_reason,
            'lost_at' => $request->status === 'lost' ? $request->lost_at : null,
            // WhatsApp Business Integration
            'whatsapp_enabled' => $request->boolean('whatsapp_enabled', false),
            'meta_business_id' => $request->meta_business_id,
            'whatsapp_id' => $request->whatsapp_id,
            'whatsapp_provider' => $request->whatsapp_provider,
            'message_quality' => $request->message_quality,
            'messaging_tier' => $request->messaging_tier,
            'meta_business_verified' => $request->boolean('meta_business_verified', false),
            'whatsapp_business_verified' => $request->boolean('whatsapp_business_verified', false),
            // Taqnyat Integration
            'taqnyat_id' => $request->taqnyat_id,
            'taqnyat_username' => $request->taqnyat_username,
        ]);

        // Sync tags
        $currentTagIds = $business->tags()->pluck('tags.id')->toArray();
        $newTagIds = $request->tag_ids ?? [];

        // Remove tags that are no longer selected
        $tagsToRemove = array_diff($currentTagIds, $newTagIds);
        foreach ($tagsToRemove as $tagId) {
            $tag = Tag::find($tagId);
            if ($tag) {
                $business->removeTag($tag);
            }
        }

        // Add new tags
        $tagsToAdd = array_diff($newTagIds, $currentTagIds);
        foreach ($tagsToAdd as $tagId) {
            $tag = Tag::find($tagId);
            if ($tag) {
                $business->assignTag($tag, Auth::user());
            }
        }

        // Sync products
        $currentProductIds = $business->products()->pluck('products.id')->toArray();
        $newProductIds = $request->product_ids ?? [];

        // Remove products that are no longer selected
        $productsToRemove = array_diff($currentProductIds, $newProductIds);
        foreach ($productsToRemove as $productId) {
            $product = Product::find($productId);
            if ($product) {
                $business->removeProduct($product);
            }
        }

        // Add new products
        $productsToAdd = array_diff($newProductIds, $currentProductIds);
        foreach ($productsToAdd as $productId) {
            $product = Product::find($productId);
            if ($product) {
                $business->assignProduct($product, Auth::user());
            }
        }

        // Log business update activity
        BusinessActivity::logUserAction(
            $business,
            'business_update',
            'Business information updated',
            'Business details have been modified',
            [
                'updated_fields' => array_keys($request->only([
                    'name', 'arabic_name', 'brand_name', 'legal_name', 'description', 'email', 'phone',
                    'primary_phone', 'address', 'city', 'state', 'country', 'postal_code',
                    'website', 'website_url', 'tax_id', 'status', 'churn_reason', 'is_active'
                ])),
                'tags_added' => count($tagsToAdd),
                'tags_removed' => count($tagsToRemove),
                'products_added' => count($productsToAdd),
                'products_removed' => count($productsToRemove),
            ]
        );

        // Log status transition if changed
        if ($oldStatus !== $newStatus) {
            $this->logStatusTransition($business, $oldStatus, $newStatus);
        }

        return redirect()->route('business.index')
                        ->with('success', 'Business updated successfully.');
    }

    /**
     * Log business status transition
     */
    private function logStatusTransition(Business $business, string $oldStatus, string $newStatus): void
    {
        $severity = 'info';
        $title = "Status changed from {$oldStatus} to {$newStatus}";
        $description = "Business status has been updated";

        // Set severity based on transition type
        if ($newStatus === 'churned') {
            $severity = 'warning';
            $description = "Business has been marked as churned";
        } elseif ($oldStatus === 'lead' && $newStatus === 'deal') {
            $severity = 'success';
            $description = "Lead has been converted to a deal";
        } elseif ($oldStatus === 'deal' && $newStatus === 'customer') {
            $severity = 'success';
            $description = "Deal has been closed and converted to customer";
        } elseif ($newStatus === 'partner') {
            $severity = 'success';
            $description = "Business has been upgraded to partner status";
        }

        BusinessActivity::createActivity([
            'business_id' => $business->id,
            'user_id' => Auth::id(),
            'type' => 'status_change',
            'category' => 'business_update',
            'severity' => $severity,
            'title' => $title,
            'description' => $description,
            'metadata' => [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'transition_type' => "{$oldStatus}_to_{$newStatus}",
                'changed_by' => Auth::user()->name ?? 'System',
                'changed_at' => now()->toISOString(),
            ],
            'is_visible' => true,
            'is_system_generated' => false,
        ]);
    }

    /**
     * Remove the specified business
     */
    public function destroy(Business $business): RedirectResponse
    {
        $business->delete();

        return redirect()->route('business.index')
                        ->with('success', 'Business deleted successfully.');
    }

    /**
     * Show business users management
     */
    public function users(Business $business): View
    {
        $business->load(['users.role']);
        $availableUsers = User::whereDoesntHave('businesses', function ($query) use ($business) {
            $query->where('business_id', $business->id);
        })->get();

        return view('plugins.business::users', compact('business', 'availableUsers'));
    }

    /**
     * Assign a user to the business
     */
    public function assignUser(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|in:member,manager,admin',
        ]);

        $user = User::findOrFail($request->user_id);

        if ($business->hasUser($user)) {
            return redirect()->back()->with('error', 'User is already assigned to this business.');
        }

        $business->assignUser($user, $request->role);

        return redirect()->back()->with('success', 'User assigned to business successfully.');
    }

    /**
     * Remove a user from the business
     */
    public function removeUser(Business $business, User $user): RedirectResponse
    {
        if (!$business->hasUser($user)) {
            return redirect()->back()->with('error', 'User is not assigned to this business.');
        }

        $business->removeUser($user);

        return redirect()->back()->with('success', 'User removed from business successfully.');
    }

    /**
     * Show business reports
     */
    public function reports(Business $business): View
    {
        $business->load(['users', 'creator']);

        $stats = [
            'total_users' => $business->users()->count(),
            'active_users' => $business->activeUsers()->count(),
            'managers' => $business->users()->wherePivot('role', 'manager')->count(),
            'admins' => $business->users()->wherePivot('role', 'admin')->count(),
        ];

        return view('plugins.business::reports', compact('business', 'stats'));
    }

}
